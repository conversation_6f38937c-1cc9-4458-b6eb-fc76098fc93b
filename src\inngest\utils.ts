import { Sandbox } from "@e2b/code-interpreter";
import { AgentResult, TextMessage } from "@inngest/agent-kit";


export async function getSandbox(SandboxId: string) {
    try {
        const sandbox = await Sandbox.connect(SandboxId);
        return sandbox;
    } catch (error) {
        console.error(`Failed to connect to sandbox ${SandboxId}:`, error);
        // If connection fails, try to create a new sandbox
        console.log('Creating new sandbox...');
        const newSandbox = await Sandbox.create("polo");
        return newSandbox;
    }
}

export function lastAssistantTextMessageContent(result: AgentResult){
    const lastAssistantTextMessageIndex = result.output.findLastIndex(
        (message) => message.role === "assistant",
    );

    const message = result.output[lastAssistantTextMessageIndex] as
       | TextMessage
       | undefined;

    return message?.content
        ? typeof message.content === "string"
            ? message.content
            : message.content.map((c) => c.text).join("")
        : undefined;
};