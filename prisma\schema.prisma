// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum MessageRole {
  USER
  ASSISTANT
}

enum MessageType {
  RESULT
  ERROR
}

model message {
  id String @id @default(uuid())
  content String
  role MessageRole
  createAt DateTime @default(now())
  updateAt DateTime @updatedAt

  fragment Fragment?
}

model Fragment {
  id String @id @default(uuid())
  messageId String @unique
  message message @relation(fields: [messageId],references: [id],onDelete: Cascade)

  sandboxUrl String
  title String
  files Json

  createAt DateTime @default(now())
  updateAt DateTime @updatedAt
}