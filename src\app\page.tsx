"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useMutation } from "@tanstack/react-query";
import { useTRPC } from "@/trpc/client";
import { useState } from "react";
import { toast } from "sonner";
const page = () =>{
  const [value, setValue] = useState("");

  const trpc = useTRPC();
  const createMessage = useMutation(trpc.messages.create.mutationOptions({
    onSuccess: () => {
      toast.success("message created")
    }
  }));
  return (
    <div className="p-4 max-w-7xl mx-auto">
      <input type="text" value={value} onChange={(e) => setValue(e.target.value)} />
      <Button disabled={createMessage.isPending}
       onClick={() => createMessage.mutate({Value:value})}
      >
        Invoke background job
      </Button>
    </div>
  );
};

export default page;